# HighFive Local Development Environment 🚀

Welcome to HighFive Veterinary Clinic Management System! This guide will help you set up the complete development environment in just a few minutes. This all-in-one Docker setup gets you coding fast with both the Laravel backend and React frontend.

---

## 📋 Prerequisites

Before you start, make sure you have these installed on your machine:

### Required Software
- **Docker Desktop** (4.0+) - [Download here](https://www.docker.com/products/docker-desktop/)
- **Git** - [Download here](https://git-scm.com/downloads)
- **Node.js & npm** (18+) - [Download here](https://nodejs.org/) *(for frontend development)*

### Verify Installation
```bash
# Check if everything is installed
docker --version          # Should show Docker version 20.10+
docker-compose --version  # Should show docker-compose version 2.0+
git --version             # Should show git version
node --version            # Should show node version 18+
npm --version             # Should show npm version
```

---

## 🏗️ Project Structure

HighFive consists of three main repositories that work together:

```
your-workspace/
├── highfive-backend/         # Laravel API & Admin
├── clinic-portal-web-app/    # React Frontend (Clinic Portal)
└── highfive-dev/             # Docker Development Environment
```

---

## 🚀 Getting Started

### Step 1: Clone the Repositories

First, create a workspace directory and clone all three repositories:

```bash
# Create a workspace directory
mkdir highfive-workspace
cd highfive-workspace

# Clone all repositories
<NAME_EMAIL>:highfive-vet/highfive-backend.git
<NAME_EMAIL>:highfive-vet/clinic-portal-web-app.git
<NAME_EMAIL>:highfive-vet/highfive-dev.git

# Your directory should now look like:
# highfive-workspace/
# ├── highfive-backend/
# ├── clinic-portal-web-app/
# └── highfive-dev/
```

### Step 2: Set Up Local Hosts

Add these entries to your `/etc/hosts` file (required for local development):

```bash
# On macOS/Linux, edit with:
sudo nano /etc/hosts

# On Windows, edit:
# C:\Windows\System32\drivers\etc\hosts

# Add these lines:
127.0.0.1   highfive.local
127.0.0.1   admin.highfive.local
127.0.0.1   database
127.0.0.1   redis
127.0.0.1   opensearch
127.0.0.1   mailpit
```

### Step 3: One-Command Setup

Navigate to the dev environment and run the magical setup command:

```bash
cd highfive-dev
./dev setup
```

This single command will:
- ✅ Build and start all Docker containers
- ✅ Install PHP dependencies with Composer
- ✅ Set up the Laravel application
- ✅ Run database migrations
- ✅ Seed the database with demo data
- ✅ Install and build the React frontend (using npm)
- ✅ Configure OpenSearch indices
- ✅ Set up email testing with Mailpit
- ✅ Create environment configuration from template
- ✅ Set up both clinic portal and GPO portal applications

**Grab a coffee ☕ - this takes about 3-5 minutes on first run!**

### Environment Configuration

The setup automatically creates a `.env` file from the `env.example` template. You can customize the configuration by editing the `.env` file:

```bash
# Edit environment configuration
nano .env

# Key configuration options:
# - APP_PORT: Backend application port (default: 80)
# - DB_PORT: Database port (default: 5432)
# - REDIS_PORT: Redis port (default: 6379)
# - OPENSEARCH_PORT: Search engine port (default: 9200)
```

### Step 4: Verify Everything Works

Once setup is complete, visit these URLs to make sure everything is running:

| Service | URL | What You'll See |
|---------|-----|-----------------|
| 🌐 **Frontend App** | http://highfive.local | Main application interface |
| 🔐 **Admin Dashboard** | http://admin.highfive.local/nova | Laravel Nova admin (login: <EMAIL> / password) |
| 📊 **Queue Monitor** | http://admin.highfive.local/horizon | Laravel Horizon dashboard |
| 📧 **Email Testing** | http://localhost:8025 | Mailpit email interface |
| 🔍 **Search Engine** | http://localhost:9200 | OpenSearch API |
| 📈 **Search Dashboard** | http://localhost:5601 | OpenSearch visual interface |

---

## 🧩 Architecture Overview

Here's what's running under the hood:

| Service | Port | Description |
|---------|------|-------------|
| **Laravel Backend** | 80* | API server with Nova admin interface |
| **React Frontend** | 3000 | Clinic portal web application |
| **PostgreSQL** | 5432 | Primary database |
| **Redis** | 6379 | Cache and queue storage |
| **OpenSearch** | 9200 | Search engine with phonetic analysis |
| **OpenSearch Dashboards** | 5601 | Search data visualization |
| **Mailpit** | 8025 | Email testing and debugging |

*\*The backend port can be configured via `APP_PORT` environment variable (default: 80). Services use host-based routing (admin.highfive.local, highfive.local) rather than port-based access.*

### Frontend Applications

The React frontend includes multiple applications:
- **Clinic Portal** - Main interface for veterinary clinics
- **GPO Portal** - Group Purchasing Organization management interface

---

## 🛠️ Daily Development Commands

The `./dev` script provides everything you need for daily development. Run `./dev` or `./dev help` anytime for a complete list of all available commands!

### 🏃‍♂️ Essential Commands

```bash
# Start your day
./dev up                  # Start all services
./dev health             # Check all services are healthy
./dev check-hosts        # Verify hosts file configuration

# During development
./dev logs               # Watch all service logs
./dev test               # Run the test suite
./dev shell              # Access the app container
./dev open-all           # Open all services in browser

# End your day
./dev down               # Stop all services
```

### 🗄️ Database Commands

```bash
./dev migrate            # Run new migrations
./dev seed               # Seed with fresh test data
./dev demo               # Install demo data
./dev backup             # Backup database to file
./dev restore <file>     # Restore from backup
./dev db:wip             # Wipe database clean
```

### 🔍 Search & Indexing

```bash
./dev opensearch-reindex    # Rebuild search indices
./dev opensearch-health     # Check search engine status
./dev opensearch-indices    # List all search indices
./dev opensearch-dashboards # Open search dashboard in browser
```

### 🔧 Development Tools

```bash
./dev cache-clear        # Clear all application caches
./dev optimize          # Optimize for development
./dev queue             # Process background jobs
./dev composer-install  # Install/update PHP packages
./dev frontend          # Start frontend dev server in Docker
```

### 📊 Monitoring & Debugging

```bash
./dev health            # Check all service health
./dev status            # Show service status
./dev logs-app          # Backend application logs
./dev logs-db           # Database logs
./dev logs-redis        # Cache/queue logs
./dev logs-opensearch   # Search engine logs
./dev logs-dashboards   # OpenSearch Dashboards logs
./dev logs-horizon      # Queue worker logs
```

### 🚨 Troubleshooting Commands

```bash
./dev restart           # Restart all services
./dev reset             # ⚠️  Nuclear option: wipe everything and start fresh
```

### 🎯 Additional Commands Available

The commands above cover daily development needs. For a complete list including advanced commands like `demo:install`, `db:wip`, and more, run:

```bash
./dev                   # Shows full help with all available commands
```

### 🔧 New Features

- **Host Configuration Check**: `./dev check-hosts` - Verify your hosts file is properly configured
- **Service Health Monitoring**: `./dev health` - Comprehensive health check of all services
- **Browser Integration**: `./dev open-all` - Open all services in your default browser

---

## 🧑‍💻 Typical Development Workflow

Here's how most developers use HighFive day-to-day:

```bash
# Morning: Start your environment
cd highfive-dev
./dev up

# Check everything is healthy
./dev health

# Start coding! 
# - Edit files in highfive-backend/ or highfive-web-app/
# - Changes auto-reload thanks to Docker volumes
# - Check logs if needed: ./dev logs

# Run tests before committing
./dev test

# Evening: Stop everything
./dev down
```

---

## 🆘 Troubleshooting Guide

### Common Issues & Solutions

**🚨 "502 Bad Gateway" on frontend?**
```bash
./dev logs              # Check all service logs (includes frontend)
./dev restart           # Restart all services
```

**🚨 Database connection errors?**
```bash
./dev health           # Check if database is running
./dev logs-db          # Check database logs
./dev restart          # Restart all services (including database)
```

**🚨 Search not working?**
```bash
./dev opensearch-health     # Check OpenSearch status
./dev opensearch-reindex    # Rebuild search indices
```

**🚨 Permission errors?**
```bash
./dev shell
chmod -R 775 storage bootstrap/cache
```

**🚨 Everything is broken?**
```bash
./dev reset    # Nuclear option: destroy and rebuild everything
./dev setup    # Then run setup again
```

### Getting Help

1. **Check hosts configuration**: `./dev check-hosts`
2. **Check service health**: `./dev health`
3. **Check logs**: `./dev logs` or specific service logs
4. **Try restarting**: `./dev restart`
5. **Ask the team**: Post in the #engineering Slack channel with error logs
6. **Last resort**: `./dev reset` and `./dev setup`

---

## 💡 Pro Tips & Advanced Usage

### Frontend Development Options

**Option 1: Dockerized (Recommended for consistency)**
```bash
./dev frontend          # Runs frontend in Docker at http://highfive.local
```

**Option 2: Local Development (Faster hot-reload)**
```bash
cd ../clinic-portal-web-app
npm install
npm run start          # Runs at http://localhost:3000

# For GPO Portal specifically:
npm run start:gpo      # Runs GPO portal interface
```



### Running Custom Commands

```bash
# Run any Laravel Artisan command
./dev shell
php artisan make:controller MyController

# Run Composer commands
./dev shell
composer require some/package

# Access database directly
./dev shell-db
psql -U highfive -d highfive
```

### Performance Tips

- Use `./dev optimize` after pulling new code
- Use `./dev cache-clear` if you see stale data
- Monitor resource usage with `./dev logs` and `./dev health`

---

## 📚 Additional Resources

- **[Laravel Documentation](https://laravel.com/docs)** - Backend framework
- **[React Documentation](https://react.dev/)** - Frontend framework  
- **[OpenSearch Documentation](https://opensearch.org/docs/)** - Search engine
- **[Docker Compose Documentation](https://docs.docker.com/compose/)** - Container orchestration
- **[Laravel Nova Documentation](https://nova.laravel.com/docs/)** - Admin panel

---

## 🎨 Built with ❤️ by the HighFive Team

Questions? Issues? Ideas? We're here to help!
- 📞 **Slack**: #engineering channel  
- 🐛 **Issues**: Create a GitHub issue
- 📖 **Docs**: This README and inline code comments

**Happy coding! 🚀** 