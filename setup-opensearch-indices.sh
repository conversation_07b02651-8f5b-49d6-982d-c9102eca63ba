#!/bin/bash

# HighFive OpenSearch Index Setup Script
# This script sets up OpenSearch indices with proper templates and mappings

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# OpenSearch connection details (no authentication - security disabled)
OPENSEARCH_URL="http://localhost:9200"

# Function to wait for OpenSearch
wait_for_opensearch() {
    print_status "Waiting for OpenSearch to be ready..."
    
    timeout=120
    while ! curl -s -f "$OPENSEARCH_URL/_cluster/health" > /dev/null; do
        sleep 5
        timeout=$((timeout - 5))
        if [ $timeout -le 0 ]; then
            print_error "OpenSearch failed to start within 120 seconds"
            exit 1
        fi
    done
    
    print_success "OpenSearch is ready"
}

# Function to create index template
create_index_template() {
    local template_name=$1
    local template_file=$2
    
    print_status "Creating index template: $template_name"
    
    if [ ! -f "$template_file" ]; then
        print_warning "Template file not found: $template_file, skipping"
        return 0
    fi
    
    response=$(curl -s -w "%{http_code}" \
        -X PUT "$OPENSEARCH_URL/_index_template/$template_name" \
        -H "Content-Type: application/json" \
        -d @"$template_file")
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        print_success "Index template '$template_name' created successfully"
    else
        print_error "Failed to create index template '$template_name'. HTTP code: $http_code"
        echo "Response: ${response%???}"
        return 1
    fi
}

# Function to create index if it doesn't exist
create_index_if_not_exists() {
    local index_name=$1
    
    print_status "Checking if index '$index_name' exists..."
    
    if curl -s "$OPENSEARCH_URL/$index_name" > /dev/null 2>&1; then
        print_warning "Index '$index_name' already exists, skipping creation"
    else
        print_status "Creating index: $index_name"
        
        response=$(curl -s -w "%{http_code}" \
            -X PUT "$OPENSEARCH_URL/$index_name")
        
        http_code="${response: -3}"
        
        if [ "$http_code" = "200" ]; then
            print_success "Index '$index_name' created successfully"
        else
            print_error "Failed to create index '$index_name'. HTTP code: $http_code"
            echo "Response: ${response%???}"
            return 1
        fi
    fi
}

# Function to verify phonetic plugin
verify_phonetic_plugin() {
    print_status "Verifying phonetic plugin installation..."
    
    plugins=$(curl -s "$OPENSEARCH_URL/_cat/plugins?h=component")
    
    if echo "$plugins" | grep -q "analysis-phonetic"; then
        print_success "Phonetic plugin is installed and available"
    else
        print_error "Phonetic plugin is not installed. Please check OpenSearch container logs."
        return 1
    fi
}

# Function to test index templates
test_index_templates() {
    print_status "Testing index templates..."
    
    # Test products template
    if curl -s "$OPENSEARCH_URL/_index_template/products" > /dev/null 2>&1; then
        response=$(curl -s "$OPENSEARCH_URL/_index_template/products")
    
    if echo "$response" | grep -q "phonetic_filter"; then
        print_success "Products template contains phonetic configuration"
    else
        print_warning "Products template may not have phonetic configuration"
    fi
    else
        print_warning "Products template not found"
    fi
}

# Main setup function
main() {
    echo "🔍 Setting up OpenSearch indices and templates"
    echo "=============================================="
    
    # Wait for OpenSearch to be ready
    wait_for_opensearch
    
    # Verify phonetic plugin
    verify_phonetic_plugin
    
    # Create index templates (only if files exist)
    create_index_template "products" "docker/opensearch/index-templates/products.json"
    create_index_template "orders" "docker/opensearch/index-templates/orders.json"
    
    # Create indices (they will use the templates)
    create_index_if_not_exists "products"
    create_index_if_not_exists "orders"
    
    # Test templates
    test_index_templates
    
    print_success "✅ OpenSearch setup completed successfully!"
    
    echo ""
    echo "📊 Index Information:"
    echo "===================="
    curl -s "$OPENSEARCH_URL/_cat/indices?v" | grep -E "(products|orders)" || echo "No indices found yet"
    
    echo ""
    echo "🔌 Available Plugins:"
    echo "===================="
    curl -s "$OPENSEARCH_URL/_cat/plugins?h=component" | sort | uniq
}

# Run main function
main 