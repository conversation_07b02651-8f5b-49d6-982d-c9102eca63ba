# HighFive Development Environment Configuration
# Copy this file to .env and adjust values as needed

# Application
APP_ENV=local
APP_DEBUG=true
APP_URL=http://admin.highfive.local
APP_NAME="HighFive Development"

# Database
DB_CONNECTION=pgsql
DB_HOST=database
DB_PORT=5432
DB_DATABASE=highfive
DB_USERNAME=highfive
DB_PASSWORD=secret

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=null
REDIS_DB=0

# OpenSearch
OPENSEARCH_HOST=opensearch
OPENSEARCH_PORT=9200
OPENSEARCH_SCHEME=http
OPENSEARCH_USERNAME=null
OPENSEARCH_PASSWORD=null

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Queue
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database-uuids

# Cache
CACHE_STORE=redis
CACHE_PREFIX=highfive_

# Session
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Logging
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Broadcasting
BROADCAST_DRIVER=log

# Services
SCOUT_DRIVER=opensearch
HORIZON_DOMAIN=admin.highfive.local

# Development Tools
TELESCOPE_ENABLED=false
PULSE_ENABLED=false

# Ports (for host machine access)
APP_PORT=80
DB_PORT=5432
REDIS_PORT=6379
OPENSEARCH_PORT=9200
OPENSEARCH_DASHBOARDS_PORT=5601
MAILPIT_HTTP_PORT=8025 