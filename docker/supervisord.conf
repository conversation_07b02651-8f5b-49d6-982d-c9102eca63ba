[supervisord]
nodaemon=true
user=root
pidfile=/var/run/supervisord.pid
logfile=/var/www/html/storage/logs/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stdout_logfile=/var/www/html/storage/logs/nginx.log
stderr_logfile=/var/www/html/storage/logs/nginx_error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB

[program:php-fpm]
command=php-fpm --allow-to-run-as-root
autostart=true
autorestart=true
stdout_logfile=/var/www/html/storage/logs/php-fpm.log
stderr_logfile=/var/www/html/storage/logs/php-fpm_error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB

[program:horizon]
command=php /var/www/html/artisan horizon
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/horizon.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=60

[program:queue-work]
command=php /var/www/html/artisan queue:work --verbose --tries=3 --timeout=600
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/queue-work.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=60 