{"index_patterns": ["products"], "template": {"settings": {"analysis": {"filter": {"edge_ngram_filter": {"type": "edge_ngram", "min_gram": 2, "max_gram": 20, "preserve_original": true}, "phonetic_filter": {"type": "phonetic", "encoder": "metaphone", "replace": false}}, "analyzer": {"index_text_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "phonetic_filter", "edge_ngram_filter"]}, "search_text_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "stop", "porter_stem"]}}, "normalizer": {"index_keyword_normalizer": {"type": "custom", "filter": ["lowercase", "asciifolding"]}}}}, "mappings": {"dynamic": "strict", "properties": {"id": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "name": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer", "fields": {"keyword": {"type": "keyword", "normalizer": "index_keyword_normalizer"}}}, "description": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer"}, "manufacturer": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer", "fields": {"keyword": {"type": "keyword", "normalizer": "index_keyword_normalizer"}}}, "manufacturerSku": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "vendorSkus": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "vendorIds": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "vendorNames": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer"}, "stockStatuses": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "searchTerms": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer"}}}}}