{"index_patterns": ["orders"], "template": {"settings": {"analysis": {"filter": {"edge_ngram_filter": {"type": "edge_ngram", "min_gram": 2, "max_gram": 20, "preserve_original": true}, "phonetic_filter": {"type": "phonetic", "encoder": "metaphone", "replace": false}}, "analyzer": {"index_text_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "phonetic_filter", "edge_ngram_filter"]}, "search_text_analyzer": {"type": "custom", "tokenizer": "standard", "filter": ["lowercase", "asciifolding", "stop", "porter_stem"]}}, "normalizer": {"index_keyword_normalizer": {"type": "custom", "filter": ["lowercase", "asciifolding"]}}}}, "mappings": {"dynamic": "strict", "properties": {"id": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "clinicId": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "clinicName": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer"}, "orderNumber": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "status": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "total": {"type": "float"}, "createdAt": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}, "items": {"type": "nested", "properties": {"productId": {"type": "keyword", "normalizer": "index_keyword_normalizer"}, "productName": {"type": "text", "analyzer": "index_text_analyzer", "search_analyzer": "search_text_analyzer"}, "quantity": {"type": "integer"}, "price": {"type": "float"}}}}}}}