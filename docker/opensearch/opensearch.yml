cluster.name: highfive-opensearch
node.name: highfive-node-1

# Network settings
network.host: 0.0.0.0
http.port: 9200
transport.tcp.port: 9300

# Discovery settings for single node
discovery.type: single-node

# Security settings (disabled for development)
plugins.security.disabled: true

# Memory settings
bootstrap.memory_lock: false

# Path settings
path.data: /usr/share/opensearch/data
path.logs: /usr/share/opensearch/logs

# Cluster settings
cluster.routing.allocation.disk.threshold_enabled: false

# The phonetic plugin is automatically available once installed
# No additional configuration needed 