#!/bin/bash

# HighFive Development Environment Command Center
# This script provides all development commands in one place

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}======================================${NC}"
    echo -e "${PURPLE}  $1${NC}"
    echo -e "${PURPLE}======================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show help
show_help() {
    print_header "HighFive Development Environment"
    echo ""
    echo "Usage: ./dev <command>"
    echo ""
    echo -e "${CYAN}🚀 Environment Commands:${NC}"
    echo "  setup         Complete environment setup from scratch"
    echo "  up            Start all services"
    echo "  down          Stop all services"
    echo "  restart       Restart all services"
    echo "  reset         Reset environment (destroys all data!)"
    echo "  status        Show service status"
    echo "  logs          Show all service logs"
    echo ""
    echo -e "${CYAN}🔧 Service Commands:${NC}"
    echo "  shell         Access application container shell"
    echo "  shell-db      Access database container shell"
    echo "  shell-redis   Access Redis container shell"
    echo ""
    echo -e "${CYAN}📊 Database Commands:${NC}"
    echo "  migrate       Run database migrations"
    echo "  seed          Seed database with test data"
    echo "  demo          Install demo data"
    echo "  backup        Backup database"
    echo "  restore       Restore database from backup"
    echo "  db:wip         Wipe the database using db:wip artisan command"
    echo ""
    echo -e "${CYAN}🔍 OpenSearch Commands:${NC}"
    echo "  opensearch-reindex   Flush and reindex all data"
    echo "  opensearch-health    Check OpenSearch cluster health"
    echo "  opensearch-indices   List all OpenSearch indices"
    echo "  opensearch-plugins   List installed OpenSearch plugins"
    echo "  opensearch-dashboards Open OpenSearch Dashboards in browser"
    echo ""
    echo -e "${CYAN}🧪 Testing Commands:${NC}"
    echo "  test          Run all tests"
    echo "  precommit     Run pre-commit checks (composer install, pint, tests)"
    echo ""
    echo -e "${CYAN}⚡ Performance Commands:${NC}"
    echo "  cache-clear   Clear all caches"
    echo "  optimize      Optimize application for development"
    echo "  queue         Process queue jobs manually"
    echo ""
    echo -e "${CYAN}📦 Dependency Commands:${NC}"
    echo "  composer-install    Install PHP dependencies"
    echo "  composer-update     Update PHP dependencies"
    echo ""
    echo -e "${CYAN}🔍 Monitoring Commands:${NC}"
    echo "  health        Check health of all services"
    echo "  open-all      Open all services in browser"
    echo "  logs-app      Show application logs"
    echo "  logs-db       Show database logs"
    echo "  logs-redis    Show Redis logs"
    echo "  logs-opensearch Show OpenSearch logs"
    echo "  logs-dashboards Show OpenSearch Dashboards logs"
    echo "  logs-horizon  Show Horizon logs (from app container)"
    echo ""
    echo -e "${CYAN}🛠️ Custom Artisan Commands:${NC}"
    echo "  demo:install   Install demo data (php artisan demo:install)"
    echo "  db:wip         Wipe the database using db:wip artisan command"
    echo -e "${CYAN}🔧 Troubleshooting Commands:${NC}"
    echo "  check-hosts   Check if hosts file is properly configured"
}

# Environment Commands
cmd_setup() {
    print_header "Setting up HighFive Development Environment"
    ./setup.sh
    print_success "Setup complete."
}

cmd_up() {
    print_status "Starting all services..."
    docker-compose up -d
    print_success "All services started"
}

cmd_down() {
    print_status "Stopping all services..."
    docker-compose down
    print_success "All services stopped"
}

cmd_restart() {
    print_status "Restarting all services..."
    docker-compose restart
    print_success "All services restarted"
}

cmd_reset() {
    print_warning "This will destroy all data and containers!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down -v --remove-orphans
        docker system prune -f
        rm -f .env
        print_success "Environment reset complete. Run './dev setup' to rebuild."
    else
        print_status "Reset cancelled"
    fi
}

cmd_status() {
    print_header "Service Status"
    docker-compose ps
}

cmd_logs() {
    docker-compose logs -f
}

# Service Commands
cmd_shell() {
    docker-compose exec app sh
}

cmd_shell_db() {
    docker-compose exec database sh
}

cmd_shell_redis() {
    docker-compose exec redis sh
}

# Database Commands
cmd_migrate() {
    print_status "Running database migrations..."
    docker-compose exec app php artisan migrate
    print_success "Migrations completed"
}

cmd_seed() {
    print_status "Seeding database..."
    docker-compose exec app php artisan db:seed
    print_success "Database seeded"
}

cmd_demo() {
    print_status "Installing demo data..."
    docker-compose exec app php artisan demo:install
    print_success "Demo data installed"
}

cmd_backup() {
    timestamp=$(date +"%Y%m%d_%H%M%S")
    filename="backup_${timestamp}.sql"
    print_status "Creating database backup: $filename"
    docker-compose exec database pg_dump -U highfive highfive > "$filename"
    print_success "Database backup created: $filename"
}

cmd_restore() {
    if [ -z "$2" ]; then
        print_error "Please specify backup file: ./dev restore <backup_file>"
        exit 1
    fi
    print_status "Restoring database from: $2"
    docker-compose exec -T database psql -U highfive -d highfive < "$2"
    print_success "Database restored"
}

# OpenSearch Commands
cmd_opensearch_reindex() {
    print_status "Checking if indices exist before flushing..."
    
    # Check if products index exists
    if curl -s -f "http://localhost:9200/products" > /dev/null 2>&1; then
        print_status "Products index exists, flushing..."
        docker-compose exec app php artisan scout:flush "App\\Models\\Product" || true
    else
        print_warning "Products index does not exist, skipping flush"
    fi
    
    print_status "Importing/reindexing all data..."
    docker-compose exec app php artisan scout:import "App\\Models\\Product" || true
    print_success "Reindexing completed"
}

cmd_opensearch_health() {
    print_status "Checking OpenSearch cluster health..."
    curl -s http://localhost:9200/_cluster/health?pretty
}

cmd_opensearch_indices() {
    print_status "Listing OpenSearch indices..."
    curl -s http://localhost:9200/_cat/indices?v
}

cmd_opensearch_plugins() {
    print_status "Listing OpenSearch plugins..."
    curl -s http://localhost:9200/_cat/plugins?v
}

cmd_opensearch_dashboards() {
    print_status "Opening OpenSearch Dashboards in browser..."
    if command -v open >/dev/null 2>&1; then
        open http://localhost:5601
    elif command -v xdg-open >/dev/null 2>&1; then
        xdg-open http://localhost:5601
    else
        print_status "Please open http://localhost:5601 in your browser"
    fi
}

# Testing Commands
cmd_test() {
    # create database if not exists
    docker-compose exec -T database psql -U highfive -c "CREATE DATABASE highfive_test;" 2>/dev/null || true
    # run tests
    print_status "Running all tests..."
    docker-compose exec app php artisan test
}

# Performance Commands
cmd_cache_clear() {
    print_status "Clearing all caches..."
    docker-compose exec app php artisan cache:clear
    docker-compose exec app php artisan config:clear
    docker-compose exec app php artisan route:clear
    docker-compose exec app php artisan view:clear
    print_success "All caches cleared"
}

cmd_optimize() {
    print_status "Optimizing application for development..."
    docker-compose exec app php artisan config:cache
    docker-compose exec app php artisan route:cache
    print_success "Application optimized"
}

cmd_queue() {
    print_status "Processing queue jobs..."
    docker-compose exec app php artisan queue:work --once
}

# Dependency Commands
cmd_composer_install() {
    print_status "Installing PHP dependencies..."
    # Install dependencies (auth.json and SSH keys are now mounted as volumes)
    docker-compose exec app composer install --optimize-autoloader --no-interaction
    print_success "PHP dependencies installed"
}

cmd_composer_update() {
    print_status "Updating PHP dependencies..."
    docker-compose exec app composer update
    print_success "PHP dependencies updated"
}

# Monitoring Commands
cmd_health() {
    print_status "Checking service health..."
    docker-compose ps
    echo ""
    
    print_status "Testing service connectivity..."
    
    # Test database
    if docker-compose exec -T database pg_isready -U highfive >/dev/null 2>&1; then
        print_success "✅ Database (database:5432) - Healthy"
    else
        print_error "❌ Database (database:5432) - Unhealthy"
    fi
    
    # Test Redis
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        print_success "✅ Redis (redis:6379) - Healthy"
    else
        print_error "❌ Redis (redis:6379) - Unhealthy"
    fi
    
    # Test OpenSearch
    if curl -s http://opensearch:9200/_cluster/health >/dev/null 2>&1; then
        print_success "✅ OpenSearch (opensearch:9200) - Healthy"
    else
        print_error "❌ OpenSearch (opensearch:9200) - Unhealthy"
    fi
    
    # Test application
    if curl -s http://admin.highfive.local/up >/dev/null 2>&1; then
        print_success "✅ Application (admin.highfive.local) - Healthy"
    else
        print_error "❌ Application (admin.highfive.local) - Unhealthy"
    fi
    
    # Test Mailpit
    if curl -s http://mailpit:8025 >/dev/null 2>&1; then
        print_success "✅ Mailpit (mailpit:8025) - Healthy"
    else
        print_error "❌ Mailpit (mailpit:8025) - Unhealthy"
    fi
    
    echo ""
    print_status "OpenSearch cluster health:"
    curl -s http://opensearch:9200/_cluster/health?pretty | grep -E "(status|cluster_name)"
}

cmd_open_all() {
    print_status "Opening all services in browser..."
    
    # Function to open URL based on OS
    open_url() {
        if command -v open >/dev/null 2>&1; then
            open "$1"
        elif command -v xdg-open >/dev/null 2>&1; then
            xdg-open "$1"
        else
            print_status "Please open $1 in your browser"
        fi
    }
    
    # Open all services
    open_url "http://highfive.local"
    sleep 1
    open_url "http://admin.highfive.local/nova"
    sleep 1
    open_url "http://admin.highfive.local/horizon"
    sleep 1
    open_url "http://localhost:5601"
    sleep 1
    open_url "http://localhost:8025"
    
    print_success "All services opened in browser"
}

cmd_check_hosts() {
    print_status "Checking hosts file configuration..."
    
    # Check if hosts file exists
    if [ ! -f "/etc/hosts" ]; then
        print_error "Hosts file not found at /etc/hosts"
        return 1
    fi
    
    # Required host entries
    required_hosts=(
        "highfive.local"
        "admin.highfive.local"
        "database"
        "redis"
        "opensearch"
        "mailpit"
    )
    
    missing_hosts=()
    
    for host in "${required_hosts[@]}"; do
        if grep -q "127.0.0.1.*$host" /etc/hosts; then
            print_success "✅ $host - Configured"
        else
            print_error "❌ $host - Missing"
            missing_hosts+=("$host")
        fi
    done
    
    if [ ${#missing_hosts[@]} -eq 0 ]; then
        print_success "All required hosts are configured!"
    else
        echo ""
        print_warning "Missing host entries. Add these to /etc/hosts:"
        for host in "${missing_hosts[@]}"; do
            echo "127.0.0.1   $host"
        done
    fi
}

cmd_logs_app() {
    docker-compose logs -f app
}

cmd_logs_db() {
    docker-compose logs -f database
}

cmd_logs_redis() {
    docker-compose logs -f redis
}

cmd_logs_opensearch() {
    docker-compose logs -f opensearch
}

cmd_logs_dashboards() {
    docker-compose logs -f opensearch-dashboards
}

cmd_logs_horizon() {
    docker exec -it highfive-app tail -f /var/log/supervisor/horizon.log
}

cmd_db_wip() {
    print_status "Wiping database (db:wip)..."
    docker-compose exec app php artisan db:wip
    print_success "Database wiped."
}

# Frontend is now run locally by developers
# Use: cd ../clinic-portal-web-app && npm run start

cmd_precommit() {
    print_header "Running Pre-commit Checks"
    
    # Check if we're in the dev environment (has docker-compose.yml)
    if [ -f "docker-compose.yml" ]; then
        # We're in the dev directory, check if backend exists
        if [ ! -d "../highfive-backend" ]; then
            print_error "highfive-backend directory not found. Please ensure the backend repository is cloned."
            exit 1
        fi
        
        print_status "Running pre-commit checks in Docker environment..."
        
        print_status "Installing PHP dependencies..."
        if ! docker-compose exec -T app composer install --optimize-autoloader --no-interaction; then
            print_error "❌ Composer install failed"
            exit 1
        fi
        print_success "✅ Composer install completed"
        
        print_status "Running code formatting with Pint..."
        if ! docker-compose exec -T app ./vendor/bin/pint; then
            print_error "❌ Code formatting failed"
            exit 1
        fi
        print_success "✅ Code formatting completed"
        
        print_status "Running tests..."
        if ! docker-compose exec -T app php artisan test; then
            print_error "❌ Tests failed"
            exit 1
        fi
        print_success "✅ Tests passed"
    else
        # We're in the backend directory, check for composer.json
        if [ ! -f "composer.json" ]; then
            print_error "composer.json not found. Please run this command from the backend directory."
            exit 1
        fi
        
        print_status "Running pre-commit checks locally..."
        
        print_status "Installing PHP dependencies..."
        if ! composer install --optimize-autoloader --no-interaction; then
            print_error "❌ Composer install failed"
            exit 1
        fi
        print_success "✅ Composer install completed"
        
        print_status "Running code formatting with Pint..."
        if ! ./vendor/bin/pint; then
            print_error "❌ Code formatting failed"
            exit 1
        fi
        print_success "✅ Code formatting completed"
        
        print_status "Running tests..."
        if ! php artisan test; then
            print_error "❌ Tests failed"
            exit 1
        fi
        print_success "✅ Tests passed"
    fi
    
    print_success "🎉 All pre-commit checks passed!"
}



# Main command dispatcher
main() {
    case "${1:-help}" in
        # Environment Commands
        setup) cmd_setup ;;
        up) cmd_up ;;
        down) cmd_down ;;
        restart) cmd_restart ;;
        reset) cmd_reset ;;
        status) cmd_status ;;
        logs) cmd_logs ;;
        
        # Service Commands
        shell) cmd_shell ;;
        shell-db) cmd_shell_db ;;
        shell-redis) cmd_shell_redis ;;
        
        # Database Commands
        migrate) cmd_migrate ;;
        seed) cmd_seed ;;
        demo) cmd_demo ;;
        backup) cmd_backup ;;
        restore) cmd_restore "$@" ;;
        
        # OpenSearch Commands
        opensearch-reindex) cmd_opensearch_reindex ;;
        opensearch-health) cmd_opensearch_health ;;
        opensearch-indices) cmd_opensearch_indices ;;
        opensearch-plugins) cmd_opensearch_plugins ;;
        opensearch-dashboards) cmd_opensearch_dashboards ;;
        
        # Testing Commands
        test) cmd_test ;;
        
        # Performance Commands
        cache-clear) cmd_cache_clear ;;
        optimize) cmd_optimize ;;
        queue) cmd_queue ;;
        
        # Dependency Commands
        composer-install) cmd_composer_install ;;
        composer-update) cmd_composer_update ;;
        
        # Monitoring Commands
        health) cmd_health ;;
        open-all) cmd_open_all ;;
        logs-app) cmd_logs_app ;;
        logs-db) cmd_logs_db ;;
        logs-redis) cmd_logs_redis ;;
        logs-opensearch) cmd_logs_opensearch ;;
        logs-dashboards) cmd_logs_dashboards ;;
        logs-horizon) cmd_logs_horizon ;;
        
        # db:wip Command
        db:wip) cmd_db_wip ;;
        
        # Custom Artisan Commands
        demo:install) docker-compose exec app php artisan demo:install ;;
        db:wip) docker-compose exec app php artisan db:wip ;;
        
        # Pre-commit Commands
        precommit) cmd_precommit ;;
        
        # Troubleshooting Commands
        check-hosts) cmd_check_hosts ;;
        
        # Help
        help|--help|-h|*) show_help ;;
    esac
}

# Run main function
main "$@" 