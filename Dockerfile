FROM php:8.3-fpm-alpine

WORKDIR /var/www/html

# Install only essential system dependencies
RUN apk add --no-cache \
    postgresql-dev \
    libzip-dev \
    icu-dev \
    curl-dev \
    oniguruma-dev \
    nginx \
    supervisor \
    curl

# Install only essential PHP extensions for Laravel
RUN docker-php-ext-install -j$(nproc) \
    pdo_pgsql \
    zip \
    opcache \
    pcntl \
    intl \
    bcmath \
    mbstring

# Install Redis extension (needed for Laravel Horizon)
RUN apk add --no-cache --virtual .build-deps autoconf build-base \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && apk del .build-deps

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configuration files will be mounted as volumes in docker-compose.yml

# Create directories and set permissions
RUN mkdir -p /var/log/supervisor /run/nginx \
    && chown -R www-data:www-data /var/www/html

EXPOSE 80

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"] 