# Version control
.git
.gitignore

# IDE and editor files
.idea/
.vscode/
*.swp
*~

# OS files
.DS_Store

# Environment files
.env
.env.*

# Dependencies (installed in container)
/vendor/
/node_modules/

# Logs and cache
/storage/logs/
/storage/framework/cache/
/storage/framework/sessions/
/storage/framework/views/
/bootstrap/cache/

# Build artifacts
/public/hot
/public/storage
/public/build/

# Testing
/coverage/
.phpunit.result.cache

# Documentation
*.md
docs/

# Development tools
Makefile
docker-compose*.yml
.dockerignore